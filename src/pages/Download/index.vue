<template>
  <div class="download_box">
    <div class="banner-box">
      <div class="banner-box_text">
        <div class="banner-box_text_title">
          {{ t('wei_nin_ti_gong_an_quan_ke_kao_de_0') }}
        </div>
        <div class="banner-box_text_title">{{ t('web3_fang_wen_ti_yan') }}</div>
        <div class="banner-box_text_desc">
          {{
            t(
              'wo_men_de_ying_yong_cai_yong_ye_jie_ling_xian_de_an_quan_biao_zhun_rang_nin_an_xin_jin_ru_jia_mi_shi_jie'
            )
          }}
        </div>
      </div>
      <img src="@assets/banner_phone.png" class="download_box_img" />
    </div>
    <div class="download_box_info">
      <div class="download_box_info_name">{{ t('download.name') }}</div>
      <div class="download_box_info_desc">{{ t('download.desc1') }}</div>
      <div class="download_box_info_desc">{{ t('download.desc2') }}</div>
      <div class="download_box_info_tip">{{ t('download.tip') }}</div>
      <div class="download_box_info_list">
        <div
          class="download_box_info_list_item"
          v-for="item in list"
          :key="item.id"
          @mouseover="onMouseover(item.id)"
          @mouseout="onMouseout(item.id)"
          @click="onOpen(item.url)"
        >
          <img :src="item.currentImg" class="download_box_info_list_item_img" />
          <div class="download_box_info_list_item_main">
            <img
              :src="item.img"
              class="download_box_info_list_item_main_logo"
            />
            <div class="download_box_info_list_item_main_label">
              <div class="download_box_info_list_item_main_label_title">
                {{ t('xia_zai_yong_yu') }}
              </div>
              <div class="download_box_info_list_item_main_label_desc">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t, locale } = useI18n();
import ios from '../../assets/ios_logo.png';
import iosOn from '../../assets/platform_bg.png';
import andriod from '../../assets/android.png';
import andriodOn from '../../assets/platform_bg_on.png';
import windows from '../../assets/windows.png';
import windowsOn from '../../assets/windows_on.png';
import macos from '../../assets/macos.png';
import macosOn from '../../assets/macos_on.png';
import linux from '../../assets/linux.png';
import linuxOn from '../../assets/linux_on.png';
const list = ref([
  {
    id: 0,
    currentImg: iosOn,
    img: ios,
    hoverImg: andriodOn,
    defaultImg: iosOn,
    name: 'iOS',
    url: 'https://xt.xinqianf167.top/s/TqWt',
  },
  {
    id: 1,
    currentImg: iosOn,
    img: andriod,
    hoverImg: andriodOn,
    defaultImg: iosOn,
    name: 'Android',
    url: 'https://xt.xinqianf167.top/Xmnf',
  },
  // {
  //     id: 2,
  //     currentImg: windows,
  //     img: windows,
  //     hoverImg: windowsOn,
  // },
  // {
  //     id: 3,
  //     currentImg: macos,
  //     img: macos,
  //     hoverImg: macosOn,
  // },
  // {
  //     id: 4,
  //     currentImg: linux,
  //     img: linux,
  //     hoverImg: linuxOn,
  // }
]);

const onMouseover = (id) => {
  list.value = list.value.map((item) => {
    if (item.id == id) {
      item.currentImg = item.hoverImg;
    }
    return item;
  });
};

const onMouseout = (id) => {
  list.value = list.value.map((item) => {
    if (item.id == id) {
      item.currentImg = item.defaultImg;
    }
    return item;
  });
};

const onOpen = (url) => {
  window.open(url, '_blank');
};
</script>
<style lang="scss" scoped>
@import url('./index.scss');
</style>

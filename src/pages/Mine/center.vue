<template>
  <div class="center_box">
    <div class="center_box_tabs">
      <div class="center_box_tabs_item">
        <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitPay || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitPay') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item">
        <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitDeliver || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitDeliver') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item">
        <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitReceive || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitReceive') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item">
        <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitAfterSale || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitAfterSale') }}
          </div>
        </div>
      </div>
    </div>
    <div class="center_box_section">
      <div class="center_box_section_title">
        {{ t('center.title') }}
        <div class="center_box_section_title_all" @click="goToOrderList">
          {{ t('center.all') }}
          <img
            src="@assets/arrowIcon.png"
            class="center_box_section_title_all_icon"
          />
        </div>
      </div>
      <div class="center_box_section_line"></div>
      <div class="center_box_section_list" v-if="recentOrders.length > 0">
        <div
          class="center_box_section_list_item"
          v-for="order in recentOrders"
          :key="order.number"
        >
          <div class="order_card">
            <div class="order_card_image">
              <img
                :src="
                  order.showUrl ||
                  'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'
                "
                class="product_img"
              />
            </div>
            <div class="order_card_content">
              <div class="order_info_line">
                <span class="order_label">{{ t('center.orderNo') }}：</span>
                <span class="order_value">{{ order.number }}</span>
                <div class="product_line">|</div>
                <span class="product_count"
                  >共 {{ order.quantity || 1 }} {{ t('center.desc') }}</span
                >
              </div>
              <div class="order_info_line">
                <span class="order_label">{{ t('center.createTime') }}：</span>
                <span class="order_value">{{
                  formatDate(order.created_at)
                }}</span>
              </div>
            </div>
            <div class="order_card_status">
              <div class="status_badge">
                {{ order.status_format || getOrderStatusText(order.status) }}
              </div>
              <div class="order_price">{{ order.total_format }}</div>
              <div class="view_detail" @click="viewOrderDetail(order)">
                {{ t('center.operate') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty_orders">
        {{ t('wish.empty') }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetAccountInfo } from '@api/account';
import { GetOrderStats } from '@api/order';
import moment from 'moment';

const { t, locale } = useI18n();
const router = useRouter();

const orderStats = ref({
  waitPay: 0,
  waitDeliver: 0,
  waitReceive: 0,
  waitAfterSale: 0,
});

const recentOrders = ref([]);
const loading = ref(false);

// 获取订单统计数据
const loadOrderStats = async () => {
  try {
    loading.value = true;

    // 使用个人信息接口获取数据
    const accountInfo = await GetAccountInfo();
    console.log('账户信息', accountInfo);

    if (accountInfo) {
      recentOrders.value =
        accountInfo.latest_orders.map((order) => ({
          ...order,
          total_format: `$${order.total}`,
          showUrl: `http://************/${order.image}`,
        })) || [];
    }
  } catch (error) {
    console.error('获取账户信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    pending_payment: t('center.waitPay'),
    processing: t('center.waitDeliver'),
    shipped: t('center.waitReceive'),
    completed: t('yi_wan_cheng'),
    cancelled: t('yi_qu_xiao'),
    refund_pending: t('center.waitAfterSale'),
  };
  return statusMap[status] || status;
};

// 跳转到订单列表
const goToOrderList = () => {
  router.push('/order');
};

// 查看订单详情
const viewOrderDetail = (order) => {
  router.push(`/orderDetail?order_number=${order.number}`);
};

onMounted(() => {
  loadOrderStats();
  GetOrderStats().then((res) => {
    console.log('订单统计', res);
    orderStats.value = {
      waitPay: res?.unpaid || 0,
      waitDeliver: res?.paid || 0,
      waitReceive: res?.shipped || 0,
      waitAfterSale: res?.completed || 0,
    };
  });
});
</script>
<style lang="scss" scoped>
@import url('./center.scss');

/* 订单卡片样式 */
.order_card {
  display: flex;
  align-items: center;
  background: #fff;
  /* border-radius: 0.8rem; */
  padding: 1.5rem;
  /* margin-bottom: 1rem; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0; */
  border-bottom: 1px solid #f0f0f0;
}

.order_card_image {
  margin-right: 1.5rem;

  .product_img {
    width: 6rem;
    height: 6rem;
    object-fit: cover;
    border-radius: 0.6rem;
    border: 1px solid #eee;
  }
}

.order_card_content {
  flex: 1;

  .order_info_line {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .order_label {
    font-size: 1.6rem;
    color: #666;
    margin-right: 0.5rem;
  }

  .order_value {
    font-size: 1.6rem;
    color: #333;
    font-weight: 500;
  }

  .product_line {
    margin: 0 0.8rem;
    color: #ccc;
  }

  .product_count {
    font-size: 1.6rem;
    color: #999;
  }
}

.order_card_status {
  display: flex;
  align-items: center;
  /* gap: 8.8rem; */

  .status_badge {
    padding: 0.4rem 0.8rem;
    background: #fff3cd;
    color: #856404;
    border-radius: 1rem;
    font-size: 1.6rem;
    font-weight: 500;
  }

  .order_price {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin: 0 10rem;
    min-width: 10rem;
  }

  .view_detail {
    font-size: 1.6rem;
    color: #6e4aeb;
    cursor: pointer;

    &:hover {
      color: #5a3dc9;
    }
  }
}

.empty_orders {
  text-align: center;
  padding: 2rem;
  color: #999;
  font-size: 1.4rem;
}

.center_box_section_title_all {
  cursor: pointer;
  font-size: 1.6rem;
}
</style>

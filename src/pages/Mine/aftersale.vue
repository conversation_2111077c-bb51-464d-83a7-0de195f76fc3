<template>
  <div class="box" v-loading="loading">
    <h4 class="box_title">{{ t('center.message') }}</h4>
    <div class="box_thead">
      <div class="box_thead_th w383">{{ t('center.product') }}</div>
      <div class="box_thead_th w149">{{ t('center.num') }}</div>
      <div class="box_thead_th w154">{{ t('center.type') }}</div>
      <div class="box_thead_th w202">{{ t('center.createTime1') }}</div>
      <div class="box_thead_th w92">{{ t('center.operate1') }}</div>
    </div>
    <div class="box_tbody" v-if="aftersaleList.length > 0">
      <div class="box_tr" v-for="item in aftersaleList" :key="item.id">
        <div class="box_tr_td w383">
          <img
            :src="getProductImage(item)"
            class="box_tr_td_img"
            :alt="item.order_product?.name"
          />
          {{ item.product_name || '' }}
        </div>
        <div class="box_tr_td price w149">x {{ item.quantity || 1 }}</div>
        <div class="box_tr_td price w154">{{ item.type_format }}</div>
        <div class="box_tr_td price w202">{{ item.created_at }}</div>
        <div class="box_tr_td del w92" @click="viewDetail(item)">
          {{ t('cha_kan') }}
        </div>
      </div>
    </div>
    <div v-else-if="!loading" class="empty_state">
      <p>{{ t('zan_wu_shou_hou_ji_lu') }}</p>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > pagination.limit" class="pagination">
      <button
        :disabled="pagination.page <= 1"
        @click="changePage(pagination.page - 1)"
        class="page_btn"
      >
        {{ t('shang_yi_ye') }}
      </button>
      <span class="page_info">
        {{ pagination.page }} /
        {{ Math.ceil(pagination.total / pagination.limit) }}
      </span>
      <button
        :disabled="
          pagination.page >= Math.ceil(pagination.total / pagination.limit)
        "
        @click="changePage(pagination.page + 1)"
        class="page_btn"
      >
        {{ t('xia_yi_ye') }}
      </button>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { GetRMAList } from '@api/account';

const { t } = useI18n();
const router = useRouter();

const loading = ref(false);
const aftersaleList = ref([]);
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
});

// 获取售后列表
const loadAftersaleList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
    };

    const response = await GetRMAList(params);
    console.log('售后列表:', response);

    aftersaleList.value = response.rmas || [];
    pagination.value.total = response.total || 0;
  } catch (error) {
    console.error('获取售后列表失败:', error);
    // ElMessage.error('获取售后列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取产品图片
const getProductImage = (item) => {
  return `http://************/${item.image}`;
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 查看详情
const viewDetail = (item) => {
  router.push(`/aftersale/detail?id=${item.id}`);
};

// 切换页码
const changePage = (page) => {
  pagination.value.page = page;
  loadAftersaleList();
};

onMounted(() => {
  loadAftersaleList();
});
</script>
<style lang="scss" scoped>
@import url('./aftersale.scss');
</style>

.address_box {
  height: 69rem;
  background: #ffffff;
  border-radius: 2rem;
  padding: 3rem 2.4rem;
  box-sizing: border-box;
  &_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3rem;
    &_action {
      display: flex;
      align-items: center;
      height: 3.6rem;
      background: #f6f6f6;
      border-radius: 3.2rem;
      border: 0.1rem solid #e7e7e7;
      padding: 0 1.6rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #242426;
      cursor: pointer;
      &_icon {
        width: 1.6rem;
        height: 1.6rem;
        margin-left: 0.4rem;
      }
    }
  }
  &_item {
    padding: 2.4rem 10rem 2.4rem 2.4rem;
    box-sizing: border-box;
    background: #f6f6f6;
    border-radius: 1.2rem;
    position: relative;
    margin-bottom: 1.6rem;
    &_user {
      margin-bottom: 0.8rem;
      display: flex;
      align-items: center;
      height: 2.3rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2rem;
      color: #242426;
      span {
        padding: 0 0.8rem;
        height: 2.3rem;
        line-height: 2.3rem;
        background: #f3effe;
        border-radius: 0.4rem;
        font-weight: 400;
        font-size: 1.4rem;
        color: #6e4aeb;
      }
    }
    &_detail {
      line-height: 2.3rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #70707b;
    }
    &_action {
      display: flex;
      align-items: center;
      position: absolute;
      right: 2.3rem;
      top: 50%;
      transform: translateY(-50%);
      &_icon {
        width: 2rem;
        height: 2rem;
        margin-left: 2rem;
        cursor: pointer;
      }
    }
  }
}

<template>
  <div class="mine_box">
    <div class="mine_box_section">
      <div class="mine_box_info" v-loading="userLoading">
        <img
          :src="userInfo.avatar || '@assets/avator.png'"
          class="mine_box_info_img"
        />
        <p class="mine_box_info_name">{{ userInfo.name || 'Guest' }}</p>
        <p class="mine_box_info_desc">
          {{ userInfo.email || 'Not logged in' }}
        </p>
      </div>
      <div class="mine_box_menu">
        <div
          v-for="item in menuList"
          :key="item.id"
          @click="onGoUrl(item.url)"
          :class="[
            'mine_box_menu_item',
            route.name == item.url ? 'active' : '',
          ]"
        >
          <img
            :src="route.name == item.url ? item.iconActive : item.icon"
            class="mine_box_menu_item_icon"
          />
          {{ t(item.name) }}
        </div>
        <div
          v-if="isLoggedIn"
          class="mine_box_menu_action"
          @click="onLogout()"
          :disabled="logoutLoading"
        >
          {{ logoutLoading ? t('tui_chu_zhong') : t('tui_chu_deng_lu') }}
        </div>
      </div>
    </div>
    <div class="mine_box_main">
      <router-view></router-view>
    </div>
  </div>
</template>
<script setup>
import HomeActive from '@assets/home_active.png';
import Home from '@assets/<EMAIL>';
import Info from '@assets/info.png';
import InfoActive from '@assets/<EMAIL>';
import Pwd from '@assets/pwd.png';
import PwdActive from '@assets/<EMAIL>';
import Order from '@assets/order.png';
import OrderActive from '@assets/<EMAIL>';
import Loaction from '@assets/location.png';
import LoactionActive from '@assets/<EMAIL>';
import Collect from '@assets/collect.png';
import CollectActive from '@assets/<EMAIL>';
import Message from '@assets/message.png';
import MessageActive from '@assets/<EMAIL>';
import { ref, reactive, onMounted, provide, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { GetAccountInfo } from '@api/account';
import { Logout } from '@api/auth';
import { useUserStore } from '@/stores/user';

const { t, locale } = useI18n();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const userLoading = ref(false);
const logoutLoading = ref(false);

// 用户信息
const userInfo = reactive({
  name: '',
  email: '',
  avatar: '',
});

// 判断用户是否已登录
const isLoggedIn = computed(() => {
  return !!userInfo.email; // 如果用户邮箱存在，则认为已登录
});

const menuList = ref([
  {
    id: 0,
    name: 'center.home',
    icon: Home,
    iconActive: HomeActive,
    url: 'Center',
  },
  {
    id: 1,
    name: 'center.info',
    icon: Info,
    iconActive: InfoActive,
    url: 'User',
  },
  {
    id: 2,
    name: 'center.pwd',
    icon: Pwd,
    url: 'Pwd',
    iconActive: PwdActive,
  },
  {
    id: 3,
    name: 'center.order',
    icon: Order,
    iconActive: OrderActive,
    url: 'Order',
  },
  {
    id: 4,
    name: 'center.location',
    icon: Loaction,
    iconActive: LoactionActive,
    url: 'Address',
  },
  {
    id: 5,
    name: 'center.collect',
    icon: Collect,
    iconActive: CollectActive,
    url: 'Collect',
  },
  {
    id: 6,
    name: 'center.message',
    icon: Message,
    iconActive: MessageActive,
    url: 'Aftersale',
  },
]);

// 获取用户信息
const loadUserInfo = async () => {
  try {
    userLoading.value = true;
    const response = await GetAccountInfo();
    console.log('用户信息:', response);

    if (response && response.customer) {
      userInfo.name = response.customer.name || '';
      userInfo.email = response.customer.email || '';
      userInfo.avatar = response.customer.avatar || '';
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    if (error.message?.includes('401')) {
      // 如果未登录，跳转到登录页
      router.push('/login');
    }
  } finally {
    userLoading.value = false;
  }
};

const onGoUrl = (url) => {
  router.push({ name: url });
};

// 退出登录
const onLogout = async () => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_tui_chu_deng_lu_ma'),
      t('que_ren_tui_chu'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    logoutLoading.value = true;

    // 调用退出登录接口
    // await Logout();

    // 使用 Pinia store 的 logout 方法清除状态
    userStore.logout();

    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user_info');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user_info');

    ElMessage.success(t('tui_chu_deng_lu_cheng_gong'));

    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error);
      // ElMessage.error('退出登录失败');
    }
  } finally {
    logoutLoading.value = false;
  }
};

// 提供刷新用户信息的方法给子组件使用
provide('refreshUserInfo', loadUserInfo);

onMounted(() => {
  loadUserInfo();
});
</script>
<style lang="scss" scoped>
@import url('./index.scss');

.mine_box_menu_action {
  cursor: pointer;
  transition: opacity 0.2s;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>

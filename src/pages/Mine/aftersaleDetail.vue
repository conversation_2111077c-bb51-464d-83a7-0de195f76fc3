<template>
  <div class="aftersale_detail_container" v-loading="loading">
    <div class="header">
      <img src="@assets/back.png" class="back_btn" @click="goBack" />
      <h1 class="title">{{ t('shou_hou_xiang_qing') }}</h1>
    </div>

    <div v-if="aftersaleDetail.id" class="content">
      <!-- 产品信息 -->
      <div class="product_info">
        <div class="product_image">
          <img
            :src="getProductImage(aftersaleDetailProduct)"
            :alt="aftersaleDetailProduct?.name"
          />
        </div>
        <div class="product_details">
          <div class="product_header">
            <h3 class="product_name">
              {{ aftersaleDetailProduct?.name || 'Ledger Flex' }}
            </h3>
            <div class="status_badge">
              {{ getStatusText(aftersaleDetail.status) }}
            </div>
          </div>
          <div class="product_quantity">
            x {{ aftersaleDetail.quantity || 1 }}
          </div>

          <div class="detail_grid">
            <div class="detail_item">
              <span class="label">{{ t('center.type') }}：</span>
              <span class="value">{{ getTypeText(aftersaleDetail.type) }}</span>
            </div>
            <div class="detail_item">
              <span class="label">{{ t('tui_huan_shu_liang') }}：</span>
              <span class="value">{{ aftersaleDetail.quantity || 1 }}</span>
            </div>
            <div class="detail_item">
              <span class="label">{{ t('yi_da_kai_bao_zhuang') }}：</span>
              <span class="value">{{
                aftersaleDetail.opened == 1 ? t('shi') : t('fou')
              }}</span>
            </div>
            <div class="detail_item">
              <span class="label">{{ t('center.createTime1') }}：</span>
              <span class="value">{{
                formatDate(aftersaleDetail.created_at)
              }}</span>
            </div>
            <div class="detail_item">
              <span class="label">{{ t('tui_huo_yuan_yin') }}：</span>
              <span class="value">{{
                getReasonText(aftersaleDetail.reason)
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 图片信息 -->
      <div class="info_section">
        <div class="section_title">
          <div class="title_bar"></div>
          <span>{{ t('tu_pian_xin_xi') }}</span>
        </div>
        <div
          class="images_container"
          v-if="aftersaleDetail.images && aftersaleDetail.images.length > 0"
        >
          <div
            class="image_item"
            v-for="(image, index) in aftersaleDetail.images"
            :key="index"
          >
            <img
              :src="getImageUrl(image)"
              :alt="`售后图片${index + 1}`"
              @click="previewImage(image)"
            />
          </div>
        </div>
        <div v-else class="no_content">{{ t('wu') }}</div>
      </div>

      <!-- 备注信息 -->
      <div class="info_section">
        <div class="section_title">
          <div class="title_bar"></div>
          <span>{{ t('center.note') }}</span>
        </div>
        <div class="comment_content">
          {{ aftersaleDetail.comment || t('wu') }}
        </div>
      </div>
    </div>

    <div v-else-if="!loading" class="empty_state">
      <p>{{ t('shou_hou_xiang_qing_jia_zai_shi_bai') }}</p>
      <div class="retry_btn" @click="loadAftersaleDetail">
        {{ t('zhong_xin_jia_zai') }}
      </div>
    </div>

    <!-- 图片预览 -->
    <el-dialog
      v-model="previewVisible"
      :title="t('tu_pian_yu_lan')"
      width="60%"
      center
    >
      <div class="preview_container">
        <img :src="previewImageUrl" alt="预览图片" class="preview_image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { GetRMADetail } from '@api/account';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const router = useRouter();
const route = useRoute();

const loading = ref(false);
const aftersaleDetail = ref({});
const aftersaleDetailProduct = ref({});
const previewVisible = ref(false);
const previewImageUrl = ref('');

// 获取售后详情
const loadAftersaleDetail = async () => {
  const rmaId = route.query.id;
  if (!rmaId) {
    ElMessage.error(t('que_shao_shou_hou_id'));
    router.back();
    return;
  }

  try {
    loading.value = true;
    const response = await GetRMADetail(rmaId);
    console.log('售后详情:', response);
    if (response.orderProduct) {
      aftersaleDetailProduct.value = response.orderProduct;
    }

    if (response.rma) {
      aftersaleDetail.value = response.rma;
    }
  } catch (error) {
    console.error('获取售后详情失败:', error);
    // ElMessage.error('获取售后详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取产品图片
const getProductImage = (product) => {
  return `http://47.237.73.30/${product.image}`;
};

// 获取图片URL
const getImageUrl = (imagePath) => {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  return `http://47.237.73.30/${imagePath}`;
};

// 预览图片
const previewImage = (imagePath) => {
  previewImageUrl.value = getImageUrl(imagePath);
  previewVisible.value = true;
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: t('dai_chu_li'),
    processing: t('chu_li_zhong'),
    approved: t('yi_pi_zhun'),
    rejected: t('yi_ju_jue'),
    completed: t('yi_wan_cheng'),
    cancelled: t('yi_qu_xiao'),
  };
  return statusMap[status] || status;
};

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    return: t('tui_huo'),
    exchange: t('huan_huo'),
    repair: t('wei_xiu'),
  };
  return typeMap[type] || type;
};

// 获取原因文本
const getReasonText = (reason) => {
  const reasonMap = {
    not_received: t('wei_shou_dao_huo'),
    damaged: t('shang_pin_sun_huai'),
    not_match: t('shang_pin_bu_fu'),
    quality_issue: t('zhi_liang_wen_ti'),
    other: t('qi_ta'),
  };
  return reasonMap[reason] || reason;
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 返回上一页
const goBack = () => {
  router.back();
};

onMounted(() => {
  loadAftersaleDetail();
});
</script>

<style lang="scss" scoped>
@import url('./aftersaleDetail.scss');
</style>

<template>
  <div class="goods_view">
    <div class="goods_view_title">{{ t('goods.title') }}</div>
    <goodsList :products="productList" />
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import { GetProductList } from '@api/product';
import goodsList from '@components/goodsList.vue';
import { useI18n } from 'vue-i18n';
import { currencyUpdated } from '@utils/eventBus';

import { useRoute } from 'vue-router';
const route = useRoute();

const { t } = useI18n();
const productList = ref([]);

// 获取商品列表数据
const loadProductList = async () => {
  try {
    console.log('加载商品列表...', route.query);
    const params = {};
    if (route.query.keyword) {
      params.name = route.query.keyword;
    }
    const res = await GetProductList(params);
    console.log('商品列表数据', res);
    if (res && res.items) {
      productList.value = res.items;
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
  }
};

onMounted(() => {
  loadProductList();
});

// 监听币种变化，重新加载商品列表
watch(currencyUpdated, () => {
  loadProductList();
});
</script>
<style lang="scss" scoped>
@import url('./list.scss');
</style>

<template>
  <div class="wish_box">
    <h4 class="wish_box_title">{{ t('wish.title') }}</h4>
    <p class="wish_box_desc">{{ t('wish.desc') }}</p>
  </div>
  <div class="wish_view" v-loading="loading">
    <!-- <div class="wish_view_title">{{t('goods.title')}}</div> -->
    <goodsList :products="wishlistProducts" />
    <div v-if="wishlistProducts.length === 0 && !loading" class="empty_state">
      <p>{{ t('wish.empty') }}</p>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import goodsList from '@components/goodsList.vue';
import { GetWishlist } from '@api/wishlist';
import { ElMessage } from 'element-plus';
import { currencyUpdated } from '@utils/eventBus';

const { t, locale } = useI18n();
const router = useRouter();

const wishlist = ref([]);
const loading = ref(false);

// 将收藏列表转换为goodsList组件期望的格式
const wishlistProducts = computed(() => {
  return wishlist.value.map((item) => ({
    id: item.product_id,
    name: item.product_name,
    price_format: item.price,
    images: item.image ? [item.image] : [],
    sku_id: item.id,
    wishlist_id: item.id,
    sales: item.sales,
  }));
});

// 获取收藏列表
const loadWishlist = async () => {
  try {
    loading.value = true;
    const response = await GetWishlist();
    console.log('心愿单数据:', response);

    wishlist.value = response.wishlist || [];
  } catch (error) {
    console.error('获取心愿单失败:', error);
    if (error.message?.includes('401')) {
      ElMessage.error(t('qing_xian_deng_lu'));
      router.push('/login');
    } else {
      // ElMessage.error('获取心愿单失败');
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadWishlist();
});

// 监听币种变化，重新加载收藏列表
watch(currencyUpdated, () => {
  loadWishlist();
});
</script>
<style lang="scss" scoped>
@import url('./index.scss');

.empty_state {
  text-align: center;
  padding: 3rem;
  color: #999;
  font-size: 1.6rem;

  p {
    margin: 0;
  }
}
</style>

.home_view {
  &_swiper {
    // margin-bottom: 9.7rem;

    // 轮播图箭头样式
    :deep(.swiper-button-next),
    :deep(.swiper-button-prev) {
      color: #ffffff !important;
      width: 30px !important;
      height: 30px !important;
      margin-top: -15px !important;
      margin-right: 60px;
      margin-left: 60px;

      &:after {
        font-size: 16px !important;
        font-weight: bold;
      }
    }

    // 轮播图指示器样式
    :deep(.swiper-pagination) {
      bottom: 20px !important;
    }

    :deep(.swiper-pagination-bullet) {
      width: 24px !important;
      height: 4px !important;
      background: #ffffff !important;
      border-radius: 4px !important;
      opacity: 0.1 !important;
      margin: 0 4px !important;
    }

    :deep(.swiper-pagination-bullet-active) {
      width: 60px !important;
      height: 4px !important;
      background: #ffffff !important;
      border-radius: 4px !important;
      opacity: 1 !important;
    }
  }
  &_product {
    padding: 9.7rem 30.1rem 13.2rem 30.1rem;
    box-sizing: border-box;
    background: #f6f6f6;
    &_title {
      // height: 5.6rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #000000;
      margin-bottom: 7.6rem;
      &_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 15.1rem;
        height: 5.6rem;
        padding: 0 2.5rem;
        border-radius: 3.1rem;
        border: 0.1rem solid #000000;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 2.3rem;
        color: #000000;
        cursor: pointer;
        white-space: nowrap;
        flex-wrap: nowrap;
        flex-shrink: 0;
        &_icon {
          width: 2.3rem;
          height: 2.3rem;
          margin-left: 1.2rem;
          flex-shrink: 0;
        }
      }
    }
  }
  &_vedio {
    padding: 0 30.1rem;
    box-sizing: border-box;
    background: #ffffff;
    &_url {
      width: 100%;
    }
  }
  &_banner {
    width: 100%;
    height: 90rem;
    position: relative;
    object-fit: cover;
    .home_view_banner_main {
      width: 100%;
      // height: 100%;
      position: absolute;
      top: 10rem;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24rem;
      box-sizing: border-box;
      .home_view_banner_main_left {
        .puper {
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 4rem;
          color: #242426;
          margin-bottom: 0.2rem;
        }
        .desc {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 2.4rem;
          color: #242426;
        }
      }
      .home_view_banner_main_right {
        width: 34.8rem;
        height: 5.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3.2rem;
        border: 0.1rem solid #242426;
        cursor: pointer;
        .title {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 2.4rem;
          color: #242426;
        }
        .home_view_banner_main_right_icon {
          width: 2.4rem;
          height: 2.4rem;
        }
      }
    }

    &_img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  &_ledger {
    padding: 11.1rem 30.1rem 8.6rem 30.1rem;
    box-sizing: border-box;
    background: #f6f6f6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_img {
      width: 65.4rem;
      height: 59.8rem;
    }
    &_main {
      p {
        margin-bottom: 2.7rem;
        line-height: 6.8rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 4.8rem;
        color: #242426;
        span {
          color: #6e4aeb;
        }
      }
    }
  }
  &_introduce {
    padding: 9.2rem 30.1rem 13.2rem 30.1rem;
    box-sizing: border-box;
    background: #f6f6f6;
    &_title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #242426;
      margin-bottom: 1.2rem;
      line-height: 100%;
      span {
        color: #6e4aeb;
      }
    }
    &_desc {
      height: 3.3rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 2.3rem;
      color: #242426;
      line-height: 100%;
      margin-bottom: 10.7rem;
    }
    &_list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &_item {
        position: relative;
        width: 40.7rem;
        height: 47.7rem;
        background: #ffffff;
        border-radius: 1.9rem;
        padding: 8.4rem 2.3rem 0 2.3rem;
        box-sizing: border-box;
        &_icon {
          position: absolute;
          left: 0;
          top: -7.6rem;
          width: 19.4rem;
          height: 15.3rem;
        }
        &_name {
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 2.7rem;
          color: #242426;
          margin-bottom: 1.9rem;
        }
        &_desc {
          height: 5.4rem;
          line-height: 2.7rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.9rem;
          color: #70707b;
          margin-bottom: 0.5rem;
        }
        &_img {
          width: 28.8rem;
          height: 27.6rem;
          margin: 0 auto;
        }
      }
    }
  }
}

.mobile-layout {
  padding: 0;

  // 移动端字体大小调整
  .home_view_product_title {
    font-size: 2.4rem;
    margin-bottom: 3rem;

    &_btn {
      min-width: 10rem;
      height: 4rem;
      font-size: 1.6rem;

      &_icon {
        width: 1.6rem;
        height: 1.6rem;
        margin-left: 0.8rem;
      }
    }
  }
}

// 移动端轮播图样式
.mobile-swiper {
  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    display: none;
  }

  :deep(.swiper-pagination-bullet) {
    width: 16px !important;
    height: 3px !important;
  }

  :deep(.swiper-pagination-bullet-active) {
    width: 40px !important;
  }
}

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('bian_ji_pei_song_di_zhi') : t('cart.add')"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="address-form">
      <div class="form-row">
        <el-input
          v-model="formData.name"
          :placeholder="t('center.name')"
          class="form-input"
          :class="{ error: errors.name }"
        />
      </div>

      <div class="form-row">
        <el-input
          v-model="formData.phone"
          :placeholder="t('dian_hua')"
          class="form-input"
          :class="{ error: errors.phone }"
        />
      </div>

      <div class="form-row form-row-triple">
        <el-select
          v-model="formData.country_id"
          :placeholder="t('guo_jia_di_qu')"
          class="form-select"
          :class="{ error: errors.country_id }"
          @change="handleCountryChange"
        >
          <el-option
            v-for="country in countries"
            :key="country.id"
            :label="country.name"
            :value="country.id"
          />
        </el-select>

        <el-select
          v-model="formData.zone_id"
          :placeholder="t('sheng_fen')"
          class="form-select"
          :class="{ error: errors.zone_id }"
          :disabled="!formData.country_id"
          @change="handleStateChange"
        >
          <el-option
            v-for="state in states"
            :key="state.id"
            :label="state.name"
            :value="state.id"
          />
        </el-select>

        <el-input
          v-model="formData.city"
          :placeholder="t('cheng_shi')"
          class="form-input"
          :class="{ error: errors.city }"
        />
      </div>
      <div class="form-row">
        <el-input
          v-model="formData.zipcode"
          :placeholder="t('center.pcode')"
          class="form-input"
          :class="{ error: errors.zipcode }"
        />
      </div>

      <div class="form-row">
        <el-input
          v-model="formData.address_1"
          :placeholder="t('shu_ru_xiao_qu_jie_dao_xiang_xi_di_zhi')"
          class="form-textarea"
          :class="{ error: errors.address_1 }"
        />
      </div>

      <div class="form-row">
        <div class="default-switch">
          <el-switch
            v-model="formData.default"
            :active-text="t('she_wei_mo_ren')"
            :active-value="1"
            :inactive-value="0"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-btn">{{
          t('qu_xiao')
        }}</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="loading"
          class="save-btn"
        >
          {{ t('bao_cun') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  GetAddressList,
  GetAddressDetail,
  CreateAddress,
  UpdateAddress,
  GetZones,
} from '@api/address';
import { useI18n } from 'vue-i18n';
const { t, locale } = useI18n();

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  addressData: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(['update:visible', 'success', 'close']);

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const loading = ref(false);
const isEdit = computed(() => !!props.addressData);

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  country_id: '',
  zone_id: '',
  city: '',
  address_1: '',
  address_2: '',
  zipcode: '',
  default: 0,
});

// 表单验证错误
const errors = reactive({
  name: false,
  phone: false,
  country_id: false,
  zone_id: false,
  city: false,
  address_1: false,
  zipcode: false,
});

// 地区数据
const countries = ref([]);
const states = ref([]);

// 加载国家数据
const loadCountries = async () => {
  try {
    const response = await GetAddressList();
    if (response && response.countries) {
      countries.value = response.countries.map((country) => ({
        id: country.id,
        name: country.name,
        continent_format: country.continent_format,
      }));
    }
  } catch (error) {
    console.error('获取国家列表失败:', error);
  }
};

// 国家变化处理
const handleCountryChange = async (countryId) => {
  // 只在非编辑模式或用户手动选择国家时清空值
  if (!isEdit.value || !props.addressData) {
    formData.zone_id = '';
    formData.city = '';
  }

  if (countryId) {
    try {
      const response = await GetZones(countryId);
      console.log('省份数据:', response);

      // 根据API返回结构调整，假设返回格式为 { zones: [...] } 或直接是数组
      if (response && response.zones) {
        states.value = response.zones;
      } else if (Array.isArray(response)) {
        states.value = response;
      } else {
        states.value = [];
      }
    } catch (error) {
      console.error('获取省份列表失败:', error);
      states.value = [];
    }
  } else {
    states.value = [];
  }
};

// 省份变化处理
const handleStateChange = (stateId) => {
  // 只在非编辑模式或用户手动选择省份时清空city
  if (!isEdit.value || !props.addressData) {
    formData.city = '';
  }
};

// 表单验证
const validateForm = () => {
  // 重置错误状态
  Object.keys(errors).forEach((key) => {
    errors[key] = false;
  });

  let isValid = true;

  if (!formData.name.trim()) {
    errors.name = true;
    isValid = false;
  }

  if (!formData.phone.trim()) {
    errors.phone = true;
    isValid = false;
  }

  if (!formData.country_id) {
    errors.country_id = true;
    isValid = false;
  }

  if (!formData.zone_id) {
    errors.zone_id = true;
    isValid = false;
  }

  if (!formData.city.trim()) {
    errors.city = true;
    isValid = false;
  }

  if (!formData.zipcode.trim()) {
    errors.zipcode = true;
    isValid = false;
  }

  if (!formData.address_1.trim()) {
    errors.address_1 = true;
    isValid = false;
  }

  if (!isValid) {
    ElMessage.error(t('qing_tian_xie_wan_zheng_xin_xi'));
  }

  return isValid;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    phone: '',
    country_id: '',
    zone_id: '',
    city: '',
    address_1: '',
    zipcode: '',
    default: 0,
  });

  Object.keys(errors).forEach((key) => {
    errors[key] = false;
  });

  states.value = [];
};

// 加载地址详情（编辑模式）
const loadAddressDetail = async () => {
  if (!props.addressData) return;

  try {
    loading.value = true;
    const response = props.addressData;

    if (response) {
      console.log('response', response);

      // 先加载对应的地区数据，再设置表单值，以避免值被重置
      if (response.country_id) {
        await handleCountryChange(response.country_id);
      }

      // 设置表单值
      Object.assign(formData, {
        name: response.name || '',
        phone: response.phone || '',
        country_id: response.country_id || '',
        zone_id: response.zone_id || '',
        city: response.city || '',
        address_1: response.address_1 || '',
        zipcode: response.zipcode || '',
        default: response.default ? 1 : 0,
      });

      console.log('formData after set:', formData);
    }
  } catch (error) {
    console.error('获取地址详情失败:', error);
    // ElMessage.error('获取地址详情失败');
  } finally {
    loading.value = false;
  }
};

// 保存地址
const handleSave = async () => {
  if (!validateForm()) return;

  try {
    loading.value = true;
    formData.address_2 = formData.address_1 || '';

    if (isEdit.value) {
      await UpdateAddress(props.addressData.id, formData);
      ElMessage.success(t('di_zhi_geng_xin_cheng_gong'));
    } else {
      await CreateAddress(formData);
      ElMessage.success(t('di_zhi_tian_jia_cheng_gong'));
    }

    emit('success');
    handleClose();
  } catch (error) {
    console.error('保存地址失败:', error);
    const message = error.response?.data?.message || t('bao_cun_di_zhi_shi_bai');
    ElMessage.error(message);
  } finally {
    loading.value = false;
  }
};

// 关闭弹框
const handleClose = () => {
  resetForm();
  emit('close');
  emit('update:visible', false);
};

// 监听弹框打开
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      await loadCountries();
      if (isEdit.value) {
        loadAddressDetail();
      } else {
        resetForm();
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.address-form {
  padding: 0;

  .form-row {
    margin-bottom: 2rem;

    &.form-row-triple {
      display: flex;
      gap: 1rem;

      .form-select,
      .form-input {
        flex: 1;
      }
    }
  }

  .form-input,
  .form-textarea {
    width: 100%;

    &.error {
      :deep(.el-input__wrapper) {
        border: 2px solid #f56c6c !important;
        background: #fff !important;
      }
    }
  }

  .form-select {
    width: 100%;

    &.error {
      :deep(.el-select__wrapper) {
        border: 2px solid #f56c6c !important;
        background: #fff !important;
      }
    }
  }

  .default-switch {
    display: flex;
    align-items: center;

    :deep(.el-switch__label) {
      font-size: 1.4rem;
      color: #606266;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .cancel-btn {
    background: #f5f5f5;
    border-color: #dcdfe6;
    color: #606266;

    &:hover {
      background: #e6e6e6;
    }
  }

  .save-btn {
    background: #6e4aeb;
    border-color: #6e4aeb;

    &:hover {
      background: #5a3dc9;
      border-color: #5a3dc9;
    }
  }
}

:deep(.el-dialog__header) {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #ebeef5;

  .el-dialog__title {
    font-size: 1.6rem;
    font-weight: 600;
    color: #303133;
  }
}

:deep(.el-dialog__body) {
  padding: 2rem;
}

:deep(.el-dialog__footer) {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #ebeef5;
}

:deep(.el-input__wrapper) {
  background: #f3f4f8;
  border: none;
  border-radius: 32px;
  padding: 1rem 1.2rem;
  font-size: 2rem;
  height: 6rem;
  box-sizing: border-box;

  &:focus-within {
    border: 2px solid #6e4aeb;
    background: #fff;
  }
}

/* 姓名和电话输入框特殊样式 */
.form-row:first-child .form-input,
.form-row:nth-child(2) .form-input {
  width: 320px;
}

:deep(.el-select__wrapper) {
  background: #f3f4f8;
  border: none;
  border-radius: 32px;
  padding: 1rem 1.2rem;
  font-size: 2rem;
  height: 6rem;
  box-sizing: border-box;

  &:focus-within {
    border: 2px solid #6e4aeb;
    background: #fff;
  }
}

:deep(.el-textarea__inner) {
  background: #f3f4f8;
  border: none;
  border-radius: 32px;
  padding: 0 1.2rem;
  font-size: 1.4rem;
  resize: none;
  box-sizing: border-box;
  line-height: 6rem;
  height: 6rem;

  &:focus {
    border: 2px solid #6e4aeb;
    background: #fff;
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #6e4aeb;
}
</style>

// localStorage工具函数

/**
 * 设置localStorage
 * @param {string} key 键名
 * @param {any} value 值
 */
export const setStorage = (key, value) => {
  try {
    const serializedValue =
      typeof value === 'string' ? value : JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error('设置localStorage失败:', error);
  }
};

/**
 * 获取localStorage
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 * @returns {any} 存储的值
 */
export const getStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) return defaultValue;

    // 尝试解析JSON，如果失败则返回原始字符串
    try {
      return JSON.parse(item);
    } catch {
      return item;
    }
  } catch (error) {
    console.error('获取localStorage失败:', error);
    return defaultValue;
  }
};

/**
 * 移除localStorage
 * @param {string} key 键名
 */
export const removeStorage = (key) => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('移除localStorage失败:', error);
  }
};

/**
 * 清空localStorage
 */
export const clearStorage = () => {
  try {
    localStorage.clear();
  } catch (error) {
    console.error('清空localStorage失败:', error);
  }
};

/**
 * 获取token
 * @returns {string} token值
 */
export const getToken = () => {
  return getStorage('token', '');
};

/**
 * 设置token
 * @param {string} token token值
 */
export const setToken = (token) => {
  if (token) {
    setStorage('token', token);
  } else {
    removeStorage('token');
  }
};

/**
 * 获取用户信息
 * @returns {object|null} 用户信息
 */
export const getUserInfo = () => {
  return getStorage('userInfo', null);
};

/**
 * 设置用户信息
 * @param {object} userInfo 用户信息
 */
export const setUserInfo = (userInfo) => {
  if (userInfo) {
    setStorage('userInfo', userInfo);
  } else {
    removeStorage('userInfo');
  }
};

/**
 * 获取用户语言偏好
 * @returns {string} 用户选择的语言
 */
export const getLanguage = () => {
  return getStorage('language', 'en');
};

/**
 * 设置用户语言偏好
 * @param {string} language 语言代码
 */
export const setLanguage = (language) => {
  setStorage('language', language);
};

/**
 * 获取用户币种偏好
 * @returns {string} 用户选择的币种
 */
export const getCurrency = () => {
  return getStorage('currency', 'USD');
};

/**
 * 设置用户币种偏好
 * @param {string} currency 币种代码
 */
export const setCurrency = (currency) => {
  setStorage('currency', currency);
};
